export function formatTimeAgo(dateString: string): string {
  // Handle GetStream UTC timestamps that may not have timezone indicator
  let normalizedDateString = dateString;

  // If the timestamp looks like GetStream format (YYYY-MM-DDTHH:mm:ss.ssssss) without timezone
  // and doesn't end with 'Z' or have timezone offset, assume it's UTC
  if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?$/.test(dateString)) {
    normalizedDateString = dateString + "Z";
  }

  const date = new Date(normalizedDateString);
  const now = new Date();

  // Validate that the date was parsed correctly
  if (isNaN(date.getTime())) {
    console.warn("Invalid date string:", dateString);
    return "Unknown time";
  }

  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return "Just now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h`;
  }
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d`;
  }

  return date.toLocaleDateString();
}
